#!/bin/bash

# Check task details in database

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== Checking Task Details ==="
echo ""

echo "1. Checking task details via GraphQL..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name tasks { id name description taskType frequency points isActive startDate endDate } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "2. Testing completeTask with a different approach..."
echo "Let me try to manually create task progress first..."

echo ""
echo "=== Task Check Complete ==="
