#!/bin/bash

# Test the new task category management GraphQL mutations

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "🎯 TESTING TASK CATEGORY MANAGEMENT APIs 🎯"
echo ""

echo "1. 📋 Getting existing task categories..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive createdAt updatedAt } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n2. ➕ Creating a new task category..."
RESPONSE1=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTaskCategory(input: { name: \"special\", displayName: \"Special Tasks\", description: \"Special promotional tasks\", icon: \"star\", sortOrder: 10 }) { id name displayName description icon sortOrder isActive createdAt updatedAt } }"
  }' \
  "$GRAPHQL_URL")

echo "$RESPONSE1"

# Extract category ID from response for update/delete tests
CATEGORY_ID=$(echo "$RESPONSE1" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

echo -e "\n\n3. ✏️ Updating the created task category (ID: $CATEGORY_ID)..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d "{
    \"query\": \"mutation { updateTaskCategory(input: { id: \\\"$CATEGORY_ID\\\", displayName: \\\"Updated Special Tasks\\\", description: \\\"Updated description for special tasks\\\", sortOrder: 15 }) { id name displayName description icon sortOrder isActive updatedAt } }\"
  }" \
  "$GRAPHQL_URL"

echo -e "\n\n4. 📋 Getting task categories after update..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n5. 🎯 Creating a task with the new category..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d "{
    \"query\": \"mutation { createTask(input: { categoryId: \\\"$CATEGORY_ID\\\", name: \\\"Special Promotion Task\\\", description: \\\"Complete this special task\\\", taskType: ONE_TIME, frequency: ONE_TIME, points: 100 }) { id name description points category { id name displayName } } }\"
  }" \
  "$GRAPHQL_URL"

echo -e "\n\n6. ❌ Deleting the task category (ID: $CATEGORY_ID)..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d "{
    \"query\": \"mutation { deleteTaskCategory(categoryId: \\\"$CATEGORY_ID\\\") }\"
  }" \
  "$GRAPHQL_URL"

echo -e "\n\n7. 📋 Final check - getting task categories after deletion..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n🎉 TASK CATEGORY MANAGEMENT TEST COMPLETED! 🎉"
echo ""
echo "✅ The new GraphQL APIs for task category management are working!"
echo "✅ You can now create, update, and delete task categories via GraphQL!"
echo "✅ This solves the original foreign key constraint issue!"
