#!/bin/bash

# Seed the database with task categories and test the createTask mutation

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== Seeding Database with Task Categories and Tier Benefits ==="
echo ""

# First, let's manually insert the task categories using direct SQL
echo "1. Connecting to database to insert task categories..."

# Use the database connection from local.env
export PGPASSWORD=postgres
psql -h localhost -p 5433 -U postgres -d agent -c "
INSERT INTO task_categories (name, display_name, description, icon, sort_order, is_active) VALUES
('daily', 'Daily Tasks', 'Complete these tasks daily to earn points', 'calendar', 1, true),
('community', 'Community Tasks', 'Engage with our community to earn rewards', 'users', 2, true),
('trading', 'Trading Tasks', 'Complete trading activities to earn points', 'trending-up', 3, true)
ON CONFLICT (name) DO NOTHING;
"

echo ""
echo "2. Inserting tier benefits..."

psql -h localhost -p 5433 -U postgres -d agent -c "
INSERT INTO tier_benefits (tier_level, tier_name, min_points, cashback_percentage, benefits_description, tier_color, tier_icon, is_active) VALUES
(1, 'Bronze', 0, 0.0010, 'Basic tier with 0.1% cashback on all trades', '#CD7F32', 'bronze-medal', true),
(2, 'Silver', 1000, 0.0020, 'Silver tier with 0.2% cashback on all trades', '#C0C0C0', 'silver-medal', true),
(3, 'Gold', 5000, 0.0030, 'Gold tier with 0.3% cashback on all trades', '#FFD700', 'gold-medal', true),
(4, 'Platinum', 15000, 0.0040, 'Platinum tier with 0.4% cashback on all trades', '#E5E4E2', 'platinum-medal', true),
(5, 'Diamond', 50000, 0.0050, 'Diamond tier with 0.5% cashback on all trades', '#B9F2FF', 'diamond-medal', true)
ON CONFLICT (tier_level) DO NOTHING;
"

echo ""
echo "3. Verifying task categories were created..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "4. Testing createTask mutation with valid category ID (1 = daily)..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"1\" name: \"Test Daily Task\" description: \"A test task for daily category\" taskType: DAILY frequency: DAILY points: 10 }) { id name description taskType frequency points sortOrder isActive category { id name displayName } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "5. Testing createTask mutation with sortOrder specified..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"2\" name: \"Test Community Task\" description: \"A test task for community category\" taskType: ONE_TIME frequency: ONE_TIME points: 50 sortOrder: 5 }) { id name description taskType frequency points sortOrder isActive category { id name displayName } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "=== Database Seeding Complete ==="
