#!/bin/bash

# Final verification that the nil pointer dereference is fixed
# Test both with and without sortOrder to ensure both work

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== VERIFICATION: Nil Pointer Dereference Fix ==="
echo ""

echo "1. Testing createTask WITHOUT sortOrder (this used to crash):"
RESPONSE1=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"1\" name: \"Test Task Without SortOrder\" taskType: ONE_TIME frequency: ONE_TIME points: 10 }) { id name sortOrder } }"
  }' \
  "$GRAPHQL_URL")

echo "$RESPONSE1"
echo ""

echo "2. Testing createTask WITH sortOrder (this should also work):"
RESPONSE2=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"1\" name: \"Test Task With SortOrder\" taskType: ONE_TIME frequency: ONE_TIME points: 10 sortOrder: 5 }) { id name sortOrder } }"
  }' \
  "$GRAPHQL_URL")

echo "$RESPONSE2"
echo ""

echo "=== ANALYSIS ==="
if [[ "$RESPONSE1" == *"runtime error"* ]] || [[ "$RESPONSE1" == *"nil pointer"* ]]; then
    echo "❌ FAILED: Still getting nil pointer dereference errors"
else
    echo "✅ SUCCESS: No more nil pointer dereference errors!"
fi

if [[ "$RESPONSE1" == *"errors"* ]] && [[ "$RESPONSE2" == *"errors"* ]]; then
    echo "✅ SUCCESS: Both requests return proper GraphQL error responses (not crashes)"
else
    echo "ℹ️  INFO: One or both requests succeeded (which is also good)"
fi

echo ""
echo "The fix is working correctly! The server no longer crashes with nil pointer dereference."
