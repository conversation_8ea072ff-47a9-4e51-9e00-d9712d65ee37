#!/bin/bash

# Test getting task categories to see what valid categoryIds exist

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

# GraphQL endpoint
GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "Getting task categories..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive createdAt updatedAt } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n"
