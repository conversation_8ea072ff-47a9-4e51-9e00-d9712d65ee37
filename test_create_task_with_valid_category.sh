#!/bin/bash

# Test creating a task with a valid category ID
# First we need to create a category, then create a task

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

# GraphQL endpoint
GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

# Let's try to seed the database first by calling the refresh task list
echo "Refreshing task list to seed database..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { refreshTaskList }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n"

# Now get categories again
echo "Getting task categories after refresh..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive createdAt updatedAt } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n"
