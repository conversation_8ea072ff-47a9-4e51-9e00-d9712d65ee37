#!/bin/bash

# Simple test without jq to see actual responses

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== TESTING THE FIX ==="
echo ""

echo "1. Getting task categories..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n2. Testing createTask WITHOUT sortOrder..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"1\" name: \"Test Task\" taskType: DAILY frequency: DAILY points: 10 }) { id name sortOrder } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n\n3. Testing createTask WITH sortOrder..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"2\" name: \"Test Task 2\" taskType: ONE_TIME frequency: ONE_TIME points: 20 sortOrder: 5 }) { id name sortOrder } }"
  }' \
  "$GRAPHQL_URL"

echo -e "\n"
