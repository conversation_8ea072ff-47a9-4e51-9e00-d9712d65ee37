#!/bin/bash

# Seed tier benefits using GraphQL mutations

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== Seeding Tier Benefits via GraphQL ==="
echo ""

# First, let's check if tier benefits already exist
echo "1. Checking existing tier benefits..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { tierBenefits { success message data { id tierLevel tierName minPoints cashbackPercentage benefitsDescription tierColor tierIcon isActive } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "2. Creating tier benefits using admin mutations..."

# Note: Since we don't have createTierBenefit mutation in GraphQL yet,
# we'll need to create them directly in the database or add the mutation
# For now, let's check if we can use the existing admin service

echo "3. Testing completeTask mutation to see if tier benefits are now available..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation CompleteTask { completeTask(input: { taskId: \"dba8d2e3-bc0d-41f3-bf4f-98b5c5b68d83\" }) { success message pointsAwarded newTierLevel tierUpgraded } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "=== Tier Benefits Seeding Complete ==="
